package com.taobao.wireless.orange.web.controller;

import com.taobao.wireless.orange.common.thread.OThreadContext;
import com.taobao.wireless.orange.common.thread.OThreadContextHolder;
import com.taobao.wireless.orange.external.config.SwitchConfig;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;

@Controller
public class HomeController {

    @GetMapping("/")
    public String index(Model model) {
        OThreadContext threadContext = OThreadContextHolder.get();
        if (threadContext != null) {
            model.addAttribute("empId", threadContext.getWorkerId());
            model.addAttribute("userNickName", threadContext.getWorkerName());
        }

        model.addAttribute("assertDomain", "dev.g.alicdn.com");
        model.addAttribute("version", SwitchConfig.FE_VERSION);
        model.addAttribute("isAdmin", "true");
        model.addAttribute("needWholeProcess", "false");
        model.addAttribute("emailPrefix", "");
        model.addAttribute("announcement", "");

        return "index";
    }
}
