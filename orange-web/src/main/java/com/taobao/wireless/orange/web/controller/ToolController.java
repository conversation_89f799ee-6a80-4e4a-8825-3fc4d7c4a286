package com.taobao.wireless.orange.web.controller;

import com.alibaba.fastjson.JSON;
import com.google.protobuf.MessageOrBuilder;
import com.google.protobuf.util.JsonFormat;
import com.taobao.wireless.orange.common.constant.enums.Available;
import com.taobao.wireless.orange.common.constant.enums.ResourceType;
import com.taobao.wireless.orange.common.exception.CommonException;
import com.taobao.wireless.orange.common.exception.ExceptionEnum;
import com.taobao.wireless.orange.common.model.Result;
import com.taobao.wireless.orange.common.model.proto.GrayConfigProto;
import com.taobao.wireless.orange.common.model.proto.IndexProto;
import com.taobao.wireless.orange.common.model.proto.ReleaseConfigProto;
import com.taobao.wireless.orange.dal.enhanced.dao.OProbeDAO;
import com.taobao.wireless.orange.dal.enhanced.dao.OResourceDAO;
import com.taobao.wireless.orange.dal.enhanced.entity.OProbeDO;
import com.taobao.wireless.orange.dal.enhanced.entity.OResourceDO;
import com.taobao.wireless.orange.external.OssService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

@Api(tags = "工具接口")
@RestController
@RequestMapping("/api/tools")  // 复数形式
@Slf4j
public class ToolController {

    @Autowired
    private OProbeDAO probeDAO;

    @Autowired
    private OResourceDAO resourceDAO;

    @Autowired
    private OssService ossService;

    @Value("${orange.oss.bucketName}")
    private String bucketName;

    @FunctionalInterface
    private interface ProtoParser {
        MessageOrBuilder parse(byte[] bytes) throws Exception;
    }

    private final static Map<ResourceType, ProtoParser> PROTO_MAP = new HashMap<>() {
        {
            put(ResourceType.FULL_INDEX, IndexProto::parseFrom);
            put(ResourceType.INCREMENTAL_INDEX, IndexProto::parseFrom);

            put(ResourceType.FULL_RELEASE_CONFIG, ReleaseConfigProto::parseFrom);
            put(ResourceType.INCREMENTAL_RELEASE_CONFIG, ReleaseConfigProto::parseFrom);

            put(ResourceType.FULL_GRAY_CONFIG, GrayConfigProto::parseFrom);
            put(ResourceType.BETA_CONFIG, GrayConfigProto::parseFrom);
        }
    };

    @ApiOperation("查看索引信息")
    @GetMapping("/{appKey}")
    public Result<String> generate(@PathVariable("appKey") String appKey) {
        OProbeDO one = probeDAO.lambdaQuery().eq(OProbeDO::getAppKey, appKey).eq(OProbeDO::getIsAvailable, Available.Y).one();
        return Result.success(JSON.toJSONString(one));
    }

    @ApiOperation("查看配置文件内容")
    @GetMapping("/resources/{resourceId}")
    public Result<Object> getResourceContent(@PathVariable("resourceId") String resourceId) {
        OResourceDO resource = resourceDAO.lambdaQuery()
                .eq(OResourceDO::getResourceId, resourceId)
                .oneOpt()
                .orElseThrow(() -> new CommonException(ExceptionEnum.RESOURCE_NOT_EXIST));

        byte[] bytes = ossService.readData(bucketName, resourceId);

        try {
            ResourceType resourceType = resource.getType();

            MessageOrBuilder proto = PROTO_MAP.get(resourceType).parse(bytes);
            if (proto == null) {
                return Result.success(null);
            }

            String jsonStr = JsonFormat.printer()
                    .includingDefaultValueFields()
                    .print(proto);

            return Result.success(JSON.parse(jsonStr));
        } catch (Exception e) {
            log.error("getConfigContent index deserialize Exception", e);

            // fixme: 测试阶段，兼容历史 json 数据，上线移除。尝试直接将内容返回
            Result<Object> result = new Result<>();
            result.setData(JSON.parse(new String(bytes)));
            result.setSuccess(false);
            result.setMessage(ExceptionEnum.RESOURCE_DESERIALIZE_ERROR.getMessage());
            result.setCode(ExceptionEnum.RESOURCE_DESERIALIZE_ERROR.getCode());
            return result;
        }
    }
}
