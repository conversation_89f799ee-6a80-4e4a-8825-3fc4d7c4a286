package com.taobao.wireless.orange.service;

import com.alibaba.fastjson.JSON;
import com.taobao.wireless.orange.BaseTest;
import com.taobao.wireless.orange.common.constant.enums.*;
import com.taobao.wireless.orange.common.model.Pagination;
import com.taobao.wireless.orange.common.model.PaginationResult;
import com.taobao.wireless.orange.common.util.BeanUtil;
import com.taobao.wireless.orange.common.util.SerializeUtil;
import com.taobao.wireless.orange.dal.enhanced.dao.OParameterDAO;
import com.taobao.wireless.orange.dal.enhanced.dao.OReleaseOrderDAO;
import com.taobao.wireless.orange.dal.enhanced.entity.ONamespaceDO;
import com.taobao.wireless.orange.dal.enhanced.entity.OParameterDO;
import com.taobao.wireless.orange.dal.enhanced.entity.OReleaseOrderDO;
import com.taobao.wireless.orange.manager.NamespaceManager;
import com.taobao.wireless.orange.manager.ParameterManager;
import com.taobao.wireless.orange.manager.model.ONamespaceBO;
import com.taobao.wireless.orange.manager.model.ParameterBO;
import com.taobao.wireless.orange.manager.model.ParameterVersionBO;
import com.taobao.wireless.orange.service.model.ParameterDetailDTO;
import com.taobao.wireless.orange.service.model.ParameterQueryDTO;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

import static org.junit.Assert.*;

public class ParameterServiceTest extends BaseTest {

    @Autowired
    private ParameterService parameterService;

    @Autowired
    private ParameterManager parameterManager;

    @Autowired
    private NamespaceManager namespaceManager;

    @Autowired
    private OParameterDAO parameterDAO;

    @Autowired
    private OReleaseOrderDAO releaseOrderDAO;

    private String namespaceId;
    private String parameterId1;
    private String parameterId2;
    private static final String APP_KEY = "test-app";

    @Before
    public void setup() {
        // 创建命名空间
        ONamespaceBO ONamespaceBO = new ONamespaceBO();
        ONamespaceBO.setAppKey(APP_KEY);
        ONamespaceBO.setName("测试命名空间-" + UUID.randomUUID().toString().substring(0, 8));
        ONamespaceBO.setBizType(NamespaceBizType.MODULE);
        ONamespaceBO.setOwners(List.of("149016"));
        ONamespaceBO.setTesters(List.of("149016"));
        ONamespaceBO.setBizId(SerializeUtil.UUID());
        ONamespaceBO.setDescription("集成测试用命名空间");
        namespaceId = namespaceManager.create(ONamespaceBO);

        // 创建参数1
        OParameterDO parameter1 = new OParameterDO();
        parameterId1 = SerializeUtil.UUID();
        parameter1.setParameterId(parameterId1);
        parameter1.setParameterKey("test-param-1");
        parameter1.setNamespaceId(namespaceId);
        parameter1.setValueType(ParameterValueType.STRING);
        parameter1.setAppKey(APP_KEY);
        parameter1.setStatus(ParameterStatus.ONLINE);
        parameter1.setDescription("测试参数1");
        parameterDAO.save(parameter1);

        // 创建参数2
        OParameterDO parameter2 = new OParameterDO();
        parameterId2 = SerializeUtil.UUID();
        parameter2.setParameterId(parameterId2);
        parameter2.setParameterKey("test-param-2");
        parameter2.setNamespaceId(namespaceId);
        parameter2.setValueType(ParameterValueType.STRING);
        parameter2.setAppKey(APP_KEY);
        parameter2.setStatus(ParameterStatus.ONLINE);
        parameter2.setDescription("测试参数2");
        parameterDAO.save(parameter2);

        // 创建参数版本
        String releaseVersion = "v1.0";
        List<ParameterVersionBO> parameterVersions = new ArrayList<>();

        ParameterVersionBO version1 = new ParameterVersionBO();
        version1.setParameterId(parameterId1);
        version1.setParameterKey("test-param-1");
        version1.setAppKey(APP_KEY);
        version1.setNamespaceId(namespaceId);
        version1.setReleaseVersion(releaseVersion);
        version1.setPreviousReleaseVersion("");
        version1.setValueType(ParameterValueType.STRING);
        version1.setChangeType(ChangeType.CREATE);
        version1.setConditionsOrder("condition1,condition2");

        ParameterBO paramBO1 = new ParameterBO();
        paramBO1.setParameterId(parameterId1);
        paramBO1.setParameterKey("test-param-1");
        paramBO1.setNamespaceId(namespaceId);
        paramBO1.setAppKey(APP_KEY);
        paramBO1.setValueType(ParameterValueType.STRING);
        BeanUtil.copyProperties(parameter1, paramBO1);
        version1.setParameterBO(paramBO1);

        parameterVersions.add(version1);

        ParameterVersionBO version2 = new ParameterVersionBO();
        version2.setParameterId(parameterId2);
        version2.setParameterKey("test-param-2");
        version2.setAppKey(APP_KEY);
        version2.setNamespaceId(namespaceId);
        version2.setReleaseVersion(releaseVersion);
        version2.setValueType(ParameterValueType.STRING);
        version2.setChangeType(ChangeType.CREATE);
        version2.setPreviousReleaseVersion("");

        ParameterBO paramBO2 = new ParameterBO();
        paramBO2.setParameterId(parameterId2);
        paramBO2.setParameterKey("test-param-2");
        paramBO2.setNamespaceId(namespaceId);
        paramBO2.setValueType(ParameterValueType.STRING);
        paramBO2.setAppKey(APP_KEY);
        BeanUtils.copyProperties(parameter2, paramBO2);
        version2.setParameterBO(paramBO2);

        parameterVersions.add(version2);

        ONamespaceDO namespace = new ONamespaceDO();
        namespace.setAppKey(APP_KEY);
        namespace.setNamespaceId(namespaceId);

        // 创建发布单
        OReleaseOrderDO releaseOrder = new OReleaseOrderDO();
        releaseOrder.setReleaseVersion(releaseVersion);
        releaseOrder.setAppKey(APP_KEY);
        releaseOrder.setNamespaceId(namespaceId);
        releaseOrder.setBizType(ReleaseOrderBizType.NAMESPACE);
        releaseOrder.setBizId(namespaceId);
        releaseOrder.setReleaseType(ReleaseType.PUBLISH);
        releaseOrder.setStatus(ReleaseOrderStatus.INIT);
        releaseOrder.setDescription("测试发布单 - " + releaseVersion);
        releaseOrderDAO.save(releaseOrder);

        // 创建参数版本
        parameterManager.createParameterVersions(namespace, releaseVersion, parameterVersions);
    }

    @Test
    public void testQuery() {
        // 执行查询
        ParameterQueryDTO queryDTO = new ParameterQueryDTO();
        queryDTO.setNamespaceId("ec110a69f2ec44a7adaf39b3c6e83b1c");

        PaginationResult<ParameterDetailDTO> result = parameterService.query(queryDTO, new Pagination(1, 10));
        System.out.printf(JSON.toJSONString(result));
    }

    @Test
    public void testQuery_basicQuery() {
        // 执行查询
        ParameterQueryDTO queryDTO = new ParameterQueryDTO();
        queryDTO.setNamespaceId(namespaceId);

        PaginationResult<ParameterDetailDTO> result = parameterService.query(queryDTO, new Pagination(1, 10));

        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getData());
        assertEquals(2, result.getData().size());

        // 验证参数详情
        boolean foundParam1 = false;
        boolean foundParam2 = false;

        for (ParameterDetailDTO paramDetail : result.getData()) {
            if (parameterId1.equals(paramDetail.getParameterId())) {
                assertEquals("test-param-1", paramDetail.getParameterKey());
                assertEquals("v1.0", paramDetail.getReleaseVersion());
                foundParam1 = true;
            }

            if (parameterId2.equals(paramDetail.getParameterId())) {
                assertEquals("test-param-2", paramDetail.getParameterKey());
                assertEquals("v1.0", paramDetail.getReleaseVersion());
                foundParam2 = true;
            }
        }

        assertTrue("应该找到参数1", foundParam1);
        assertTrue("应该找到参数2", foundParam2);
    }

    @Test
    public void testQuery_key() {
        // 执行按键名查询
        ParameterQueryDTO queryDTO = new ParameterQueryDTO();
        queryDTO.setNamespaceId(namespaceId);
        queryDTO.setParameterKey("test-param-1");

        PaginationResult<ParameterDetailDTO> result = parameterService.query(queryDTO, new Pagination(1, 10));

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.getData().size());

        ParameterDetailDTO paramDetail = result.getData().getFirst();
        assertEquals(parameterId1, paramDetail.getParameterId());
        assertEquals("test-param-1", paramDetail.getParameterKey());
    }

    @Test
    public void testQuery_page() {
        // 第一页，每页1条记录
        ParameterQueryDTO queryDTO = new ParameterQueryDTO();
        queryDTO.setNamespaceId(namespaceId);

        PaginationResult<ParameterDetailDTO> page1Result = parameterService.query(queryDTO, new Pagination(1, 1));

        System.out.println(page1Result.getData());
        // 验证分页结果
        assertEquals(1, page1Result.getData().size());

        // 第二页，每页1条记录
        PaginationResult<ParameterDetailDTO> page2Result = parameterService.query(queryDTO, new Pagination(2, 1));

        // 验证分页结果
        assertEquals(1, page2Result.getData().size());

        // 确认两页数据不同
        String param1Id = page1Result.getData().getFirst().getParameterId();
        String param2Id = page2Result.getData().getFirst().getParameterId();
        assertNotEquals(param1Id, param2Id);
    }

    @Test
    public void testQuery_empty() {
        // 使用不存在的命名空间ID查询
        ParameterQueryDTO queryDTO = new ParameterQueryDTO();
        queryDTO.setNamespaceId("non-existent-namespace-id");

        PaginationResult<ParameterDetailDTO> result = parameterService.query(queryDTO, new Pagination(1, 10));

        // 验证结果应为空
        assertNotNull(result);
        assertTrue(result.getData().isEmpty());
    }

    @Test
    public void testQuery_status() {
        // 按状态查询
        ParameterQueryDTO queryDTO = new ParameterQueryDTO();
        queryDTO.setNamespaceId(namespaceId);

        PaginationResult<ParameterDetailDTO> result = parameterService.query(queryDTO, new Pagination(1, 10));

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.getData().size());
    }

    @Test
    public void testQuery_key_fuzzy() {
        // 模糊查询参数键名
        ParameterQueryDTO queryDTO = new ParameterQueryDTO();
        queryDTO.setNamespaceId(namespaceId);
        queryDTO.setKeyword("param");

        PaginationResult<ParameterDetailDTO> result = parameterService.query(queryDTO, new Pagination(1, 10));

        // 验证结果应包含所有参数
        assertNotNull(result);
        assertEquals(2, result.getData().size());
    }
}
