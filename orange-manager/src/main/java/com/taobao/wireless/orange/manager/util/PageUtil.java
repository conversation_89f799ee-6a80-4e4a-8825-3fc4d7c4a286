package com.taobao.wireless.orange.manager.util;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.taobao.wireless.orange.common.model.Pagination;
import com.taobao.wireless.orange.common.model.PaginationResult;
import com.taobao.wireless.orange.common.util.BeanUtil;

import java.util.Optional;
import java.util.function.Function;

public class PageUtil {
    private final static int DEFAULT_PAGE_SIZE = 10;

    /**
     * 转换为分页结果
     *
     * @param result     分页结果
     * @param targetType 目标类型
     * @param <S>        源类型
     * @param <T>        目标类型
     * @return 分页结果
     */
    public static <S, T> PaginationResult<T> convertToPaginationResult(Page<S> result, Class<T> targetType) {
        PaginationResult<T> paginationResult = BeanUtil.createFromProperties(result, PaginationResult.class);
        paginationResult.setData(BeanUtil.createListFromProperties(result.getRecords(), targetType));
        return paginationResult;
    }

    /**
     * 科里化版本的 convert 方法
     *
     * @param targetType 目标类型
     * @param <S>        源类型
     * @param <T>        目标类型
     * @return 接受 result 的函数
     */
    public static <S, T> Function<Page<S>, PaginationResult<T>> convertToPaginationResult(Class<T> targetType) {
        return result -> {
            PaginationResult<T> paginationResult = BeanUtil.createFromProperties(result, PaginationResult.class);
            paginationResult.setData(BeanUtil.createListFromProperties(result.getRecords(), targetType));
            return paginationResult;
        };
    }

    public static <S, T> Page<T> convertToPage(Page<S> result, Class<T> targetType) {
        Page<T> page = new Page<>(result.getCurrent(), result.getSize(), result.getTotal());
        page.setRecords(BeanUtil.createListFromProperties(result.getRecords(), targetType));
        return page;
    }

    public static <S, T> Function<Page<S>, Page<T>> convertToPage(Class<T> targetType) {
        return result -> {
            Page<T> page = new Page<>(result.getCurrent(), result.getSize(), result.getTotal());
            page.setRecords(BeanUtil.createListFromProperties(result.getRecords(), targetType));
            return page;
        };
    }

    public static <T> Page<T> build(Integer page, Integer size) {
        int currentPage = Optional.ofNullable(page).orElse(1);
        int pageSize = Optional.ofNullable(size).orElse(DEFAULT_PAGE_SIZE);
        return new Page<>(currentPage, pageSize);
    }

    public static <T> Page<T> build(Pagination pagination) {
        if (pagination == null) {
            return new Page<>(1, DEFAULT_PAGE_SIZE);
        }

        int currentPage = Optional.ofNullable(pagination.getPageNum()).orElse(1);
        int pageSize = Optional.ofNullable(pagination.getPageSize()).orElse(DEFAULT_PAGE_SIZE);
        return new Page<>(currentPage, pageSize);
    }
}
