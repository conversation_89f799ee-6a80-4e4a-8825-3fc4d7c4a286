package com.taobao.wireless.orange.manager.model;

import com.taobao.wireless.orange.dal.enhanced.entity.OParameterConditionVersionDO;
import com.taobao.wireless.orange.dal.enhanced.entity.OParameterDO;
import com.taobao.wireless.orange.dal.enhanced.entity.OParameterVersionDO;
import com.taobao.wireless.orange.dal.enhanced.entity.OReleaseOrderDO;
import lombok.Data;

import java.util.List;

@Data
public class ParameterBO extends OParameterDO {
    /**
     * 当前参数版本信息
     */
    private OParameterVersionDO parameterVersion;
    /**
     * 当前参数条件信息
     */
    private List<OParameterConditionVersionDO> parameterConditionVersions;
    /**
     * 发布中的发布单
     */
    private OReleaseOrderDO inPublishReleaseOrder;
    /**
     * 搜索关键字
     */
    private String keyword;
}
