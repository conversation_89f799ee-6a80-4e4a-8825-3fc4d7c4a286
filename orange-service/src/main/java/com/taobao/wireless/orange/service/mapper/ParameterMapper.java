package com.taobao.wireless.orange.service.mapper;

import com.taobao.wireless.orange.dal.enhanced.entity.OConditionDO;
import com.taobao.wireless.orange.dal.enhanced.entity.OParameterConditionVersionDO;
import com.taobao.wireless.orange.dal.enhanced.entity.OReleaseOrderDO;
import com.taobao.wireless.orange.manager.model.ParameterBO;
import com.taobao.wireless.orange.service.model.ParameterConditionDTO;
import com.taobao.wireless.orange.service.model.ParameterDetailDTO;
import com.taobao.wireless.orange.service.model.ReleaseOrderDTO;
import org.mapstruct.*;

import java.util.List;
import java.util.Map;

/**
 * MapStruct映射器，用于参数相关的对象转换
 *
 * <AUTHOR>
 * @since 2025-06-25
 */
@Mapper(componentModel = "spring")
public interface ParameterMapper {

    String DEFAULT_CONDITION_ID = "default";
    String DEFAULT_CONDITION_NAME = "默认条件";

    /**
     * 将ParameterBO转换为ParameterDetailDTO
     *
     * @param parameterBO 参数业务对象
     * @param conditionId2Condition 条件ID到条件对象的映射
     * @return 参数详情DTO
     */
    @Mapping(target = "releaseVersion", source = "parameterBO.parameterVersion.releaseVersion")
    @Mapping(target = "conditionsOrder", source = "parameterBO.parameterVersion.conditionsOrder")
    @Mapping(target = "previousReleaseVersion", source = "parameterBO.parameterVersion.previousReleaseVersion")
    @Mapping(target = "changeType", source = "parameterBO.parameterVersion.changeType")
    @Mapping(target = "parameterConditions", source = "parameterBO.parameterConditionVersions", qualifiedByName = "mapParameterConditions")
    @Mapping(target = "inPublishReleaseOrder", source = "parameterBO.inPublishReleaseOrder")
    ParameterDetailDTO toParameterDetailDTO(ParameterBO parameterBO, @Context Map<String, OConditionDO> conditionId2Condition);

    /**
     * 将OReleaseOrderDO转换为ReleaseOrderDTO
     */
    ReleaseOrderDTO toReleaseOrderDTO(OReleaseOrderDO releaseOrderDO);

    /**
     * 将参数条件版本列表转换为参数条件DTO列表
     *
     * @param conditionVersions 参数条件版本列表
     * @param conditionId2Condition 条件ID到条件对象的映射
     * @return 参数条件DTO列表
     */
    @Named("mapParameterConditions")
    default List<ParameterConditionDTO> mapParameterConditions(
            List<OParameterConditionVersionDO> conditionVersions,
            @Context Map<String, OConditionDO> conditionId2Condition) {
        
        if (conditionVersions == null) {
            return null;
        }
        
        return conditionVersions.stream()
                .map(conditionVersion -> toParameterConditionDTO(conditionVersion, conditionId2Condition))
                .toList();
    }

    /**
     * 将OParameterConditionVersionDO转换为ParameterConditionDTO
     *
     * @param conditionVersion 参数条件版本DO
     * @param conditionId2Condition 条件ID到条件对象的映射
     * @return 参数条件DTO
     */
    @Mapping(target = "conditionName", expression = "java(getConditionName(conditionVersion.getConditionId(), conditionId2Condition))")
    @Mapping(target = "conditionColor", expression = "java(getConditionColor(conditionVersion.getConditionId(), conditionId2Condition))")
    ParameterConditionDTO toParameterConditionDTO(OParameterConditionVersionDO conditionVersion, @Context Map<String, OConditionDO> conditionId2Condition);

    /**
     * 获取条件名称
     */
    default String getConditionName(String conditionId, Map<String, OConditionDO> conditionId2Condition) {
        if (DEFAULT_CONDITION_ID.equals(conditionId)) {
            return DEFAULT_CONDITION_NAME;
        }
        
        OConditionDO condition = conditionId2Condition.get(conditionId);
        return condition != null ? condition.getName() : null;
    }

    /**
     * 获取条件颜色
     */
    default String getConditionColor(String conditionId, Map<String, OConditionDO> conditionId2Condition) {
        if (DEFAULT_CONDITION_ID.equals(conditionId)) {
            return null; // 默认条件没有颜色
        }
        
        OConditionDO condition = conditionId2Condition.get(conditionId);
        return condition != null ? condition.getColor() : null;
    }
}
