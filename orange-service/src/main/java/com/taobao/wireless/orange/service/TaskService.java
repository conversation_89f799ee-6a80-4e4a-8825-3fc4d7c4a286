package com.taobao.wireless.orange.service;

import com.taobao.wireless.orange.common.constant.enums.TaskHandlerStatus;
import com.taobao.wireless.orange.common.exception.CommonException;
import com.taobao.wireless.orange.common.exception.ExceptionEnum;
import com.taobao.wireless.orange.common.model.Pagination;
import com.taobao.wireless.orange.common.model.PaginationResult;
import com.taobao.wireless.orange.common.model.Result;
import com.taobao.wireless.orange.common.thread.ThreadContextUtil;
import com.taobao.wireless.orange.common.util.BeanUtil;
import com.taobao.wireless.orange.common.util.Pipe;
import com.taobao.wireless.orange.dal.enhanced.dao.OTaskHandlerDAO;
import com.taobao.wireless.orange.dal.enhanced.entity.OTaskHandlerDO;
import com.taobao.wireless.orange.manager.TaskManager;
import com.taobao.wireless.orange.manager.model.TaskHandlerBO;
import com.taobao.wireless.orange.manager.util.PageUtil;
import com.taobao.wireless.orange.service.model.TaskDetailDTO;
import com.taobao.wireless.orange.service.model.TaskHandlerQueryDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * 任务服务
 */
@Service
public class TaskService {

    @Autowired
    private TaskManager taskManager;

    @Autowired
    private OTaskHandlerDAO taskHandlerDAO;

    /**
     * 查询我的任务列表
     *
     * @param query      查询条件
     * @param pagination 分页参数
     * @return 任务分页结果
     */
    public PaginationResult<TaskDetailDTO> queryMyTasks(TaskHandlerQueryDTO query, Pagination pagination) {
        return Pipe.of(query)
                .map(BeanUtil.createFromProperties(TaskHandlerBO.class))
                .map(q -> taskManager.queryMyTasks(q, pagination))
                .map(PageUtil.convertToPaginationResult(TaskDetailDTO.class))
                .get();
    }

    /**
     * 统计我的任务数量(按照状态分组)
     *
     * @return 任务数量
     */
    public Result<Map<TaskHandlerStatus, Long>> countMyTaskByStatus() {
        return Pipe.of(ThreadContextUtil.getWorkerId())
                .map(taskHandlerDAO::getCountGroupByStatus)
                .map(Result::new)
                .get();
    }

    /**
     * 忽略任务
     *
     * @param taskId 任务ID
     */
    public Result<Void> ignore(String taskId) {
        taskHandlerDAO.lambdaUpdate()
                .eq(OTaskHandlerDO::getTaskId, taskId)
                .eq(OTaskHandlerDO::getUserId, ThreadContextUtil.getWorkerId())
                .eq(OTaskHandlerDO::getStatus, TaskHandlerStatus.PENDING)
                .set(OTaskHandlerDO::getStatus, TaskHandlerStatus.NO_NEED)
                .update();
        return Result.success();
    }

    /**
     * 转交任务
     *
     * @param taskId 任务ID
     */
    public Result<Void> transfer(String taskId, String targetUserId) {
        boolean exists = taskHandlerDAO.lambdaQuery()
                .eq(OTaskHandlerDO::getTaskId, taskId)
                .eq(OTaskHandlerDO::getUserId, targetUserId)
                .exists();

        if (exists) {
            throw new CommonException(ExceptionEnum.PARAM_INVALID, "目标用户已经存在该任务");
        }

        taskHandlerDAO.lambdaUpdate()
                .eq(OTaskHandlerDO::getTaskId, taskId)
                .eq(OTaskHandlerDO::getUserId, ThreadContextUtil.getWorkerId())
                .eq(OTaskHandlerDO::getStatus, TaskHandlerStatus.PENDING)
                .set(OTaskHandlerDO::getUserId, targetUserId)
                .update();

        return Result.success();
    }
}