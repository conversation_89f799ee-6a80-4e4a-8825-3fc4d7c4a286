package com.taobao.wireless.orange.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.taobao.wireless.orange.common.annotation.AttributeValidate;
import com.taobao.wireless.orange.common.model.Pagination;
import com.taobao.wireless.orange.common.model.PaginationResult;
import com.taobao.wireless.orange.common.model.Result;
import com.taobao.wireless.orange.common.util.BeanUtil;
import com.taobao.wireless.orange.common.util.Pipe;
import com.taobao.wireless.orange.dal.enhanced.dao.OConditionDAO;
import com.taobao.wireless.orange.dal.enhanced.dao.OParameterDAO;
import com.taobao.wireless.orange.dal.enhanced.entity.*;
import com.taobao.wireless.orange.manager.ParameterManager;
import com.taobao.wireless.orange.manager.model.ParameterBO;
import com.taobao.wireless.orange.manager.util.PageUtil;
import com.taobao.wireless.orange.service.model.*;
import jakarta.validation.constraints.NotNull;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.taobao.wireless.orange.common.constant.Common.DEFAULT_CONDITION_ID;
import static com.taobao.wireless.orange.common.constant.Common.DEFAULT_CONDITION_NAME;

@Service
public class ParameterService {
    @Autowired
    private ParameterManager parameterManager;
    @Autowired
    private OParameterDAO parameterDAO;
    @Autowired
    private OConditionDAO conditionDAO;

    @AttributeValidate
    public PaginationResult<ParameterDetailDTO> query(@NotNull(message = "参数查询条件不能为空") ParameterQueryDTO query, Pagination pagination) {
        // 转换查询条件
        ParameterBO parameterCondition = BeanUtil.createFromProperties(query, ParameterBO.class);

        // 查询参数数据
        Page<ParameterBO> pageResult = parameterManager.query(parameterCondition, pagination);

        PaginationResult<ParameterDetailDTO> result = PageUtil.convertToPaginationResult(pageResult, ParameterDetailDTO.class);

        // 如果没有数据，直接返回空结果
        if (CollectionUtils.isEmpty(pageResult.getRecords())) {
            return result;
        }

        // 转换结果并设置
        result.setData(convertToDetailDTO(query.getNamespaceId(), pageResult.getRecords()));
        return result;
    }

    /**
     * 更新参数基础信息（不创建发布单）
     *
     * @param parameterUpdate 参数更新对象
     */
    @AttributeValidate
    public Result<Void> update(@NotNull(message = "参数更新对象不能为空") ParameterDirectUpdateDTO parameterUpdate) {
        parameterDAO.lambdaUpdate()
                .eq(OParameterDO::getParameterId, parameterUpdate.getParameterId())
                .set(StringUtils.isNotBlank(parameterUpdate.getDescription()), OParameterDO::getDescription, parameterUpdate.getDescription())
                .update();
        return Result.success();
    }

    public List<ParameterDetailDTO> convertToDetailDTO(String namespaceId, List<ParameterBO> params) {
        // 获取条件ID到名称的映射
        Map<String, OConditionDO> conditionId2Condition = conditionDAO.getConditionMapByNamespaceId(namespaceId);
        // 转换结果并设置
        return params.stream()
                .map(param -> convertToDetailDTO(param, conditionId2Condition))
                .collect(Collectors.toList());
    }

    private ParameterDetailDTO convertToDetailDTO(ParameterBO parameterBO, Map<String, OConditionDO> conditionId2Condition) {
        return Pipe.of(parameterBO)
                .map(BeanUtil.createFromProperties(ParameterDetailDTO.class))
                // 设置参数版本信息
                .apply(d -> fillParameterVersionInfo(d, parameterBO.getParameterVersion()))
                // 设置参数条件信息
                .apply(d -> fillParameterConditionInfo(d, parameterBO.getParameterConditionVersions(), conditionId2Condition))
                // 设置进行中发布单信息
                .apply(d -> d.setInPublishReleaseOrder(BeanUtil.createFromProperties(parameterBO.getInPublishReleaseOrder(), ReleaseOrderDTO.class)))
                .get();
    }

    private void fillParameterVersionInfo(ParameterDetailDTO detailDTO, OParameterVersionDO versionDO) {
        if (versionDO == null) {
            return;
        }
        detailDTO.setReleaseVersion(versionDO.getReleaseVersion());
        detailDTO.setConditionsOrder(versionDO.getConditionsOrder());
    }

    private void fillParameterConditionInfo(
            ParameterDetailDTO detailDTO,
            List<OParameterConditionVersionDO> conditionVersions,
            Map<String, OConditionDO> conditionId2Condition) {
        if (CollectionUtils.isEmpty(conditionVersions)) {
            return;
        }

        List<ParameterConditionDTO> conditionDTOList = conditionVersions.stream()
                .map(conditionVersion -> {
                    ParameterConditionDTO conditionDTO = BeanUtil.createFromProperties(
                            conditionVersion, ParameterConditionDTO.class);

                    // 设置条件信息
                    fillConditionInfo(conditionDTO, conditionId2Condition);

                    return conditionDTO;
                })
                .collect(Collectors.toList());

        detailDTO.setParameterConditions(conditionDTOList);
    }

    private void fillConditionInfo(
            ParameterConditionDTO conditionDTO,
            Map<String, OConditionDO> conditionId2Condition) {
        String conditionId = conditionDTO.getConditionId();
        if (DEFAULT_CONDITION_ID.equals(conditionId)) {
            conditionDTO.setConditionName(DEFAULT_CONDITION_NAME);
        } else {
            Optional.ofNullable(conditionId2Condition.get(conditionId))
                    .ifPresent(c -> {
                        conditionDTO.setConditionName(c.getName());
                        conditionDTO.setConditionColor(c.getColor());
                    });
        }
    }
}
